package com.ecco.data.client.dataimport.csv;

import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.core.convert.converter.Converter;

public class LocalDateTimeFromStringConverter implements Converter<String, LocalDateTime> {

    private DateTimeFormatter ddMMyyHHmm1920to2019 = DateTimeFormat.forPattern("dd/MM/yy HH:mm").withPivotYear(1970);
    private DateTimeFormatter ddMMyy1920to2019 = DateTimeFormat.forPattern("dd/MM/yy").withPivotYear(1970);

    @Override
    public LocalDateTime convert(String source) {
        try {
            return ddMMyyHHmm1920to2019.parseDateTime(source).toLocalDateTime();
        } catch (IllegalArgumentException e) {
            // we may not have a time, but need to still transform to a LocalDateTime
            return ddMMyy1920to2019.parseDateTime(source).toLocalDateTime();
        }
    }
}
