const gulp = require("gulp");
const gutil = require("gulp-util");
const typescript = require("gulp-typescript");
const babel = require("gulp-babel");
const rename = require("gulp-rename");
const terser = require("gulp-terser-js");
const jest = require("gulp-jest").default;
const merge = require("merge-stream");
const del = require("del");
const fs = require("fs");

gulp.task('clean', () => {
    return del([
        'build/**/*',
        '../../../../../../../target/classes/com/ecco/offline/staticFiles/build/**/*'
    ], {
        force: true
    });
});

const terserOptions = {
    ecma: 2016,
    compress: {
        ecma: 2016,
        // unsafe_arrows: true
    },
    output: {
        comments: false
    }
};

gulp.task("tsc", () => {
    const project = typescript.createProject("scripts/tsconfig.json", {noEmit: false});
    const tsResult = project.src()
        .pipe(project()); // Use config from project above

    return merge([
        tsResult.js
            .pipe(terser(terserOptions))
            .on('error', function (err) { gutil.log(gutil.colors.red('[Error]'), err.toString()); })
            .pipe(gulp.dest("build")),
        tsResult.dts
            .pipe((gulp.dest("build")))
    ]);
});
gulp.task("minify-js", () =>
    gulp.src([
            'jquery.browser.js',
            'jquery/jquery-timepicker.js',
            'jquery-amd/typeahead.jquery.js',
            'jquery-file-upload-9.5.8/jquery.fileupload.js',
            'lib/bloodhound.js',
            'lib/bootstrap-modal.js',
            'lib/bootstrap-modalmanager.js',
            'lib/lazy.js',
            'lib/lodash.js',
            'lib/react-bootstrap.js'
        ],
        {
            cwd: "scripts",
            base: "."
        })
    // .pipe(sourcemaps.init())
        .pipe(terser(terserOptions))
        // .pipe(sourcemaps.write(".", {
        //     includeContent: false,
        //     sourceRoot: "."
        // }))
        .pipe(rename(function (path) {
            path.extname = ".min.js"
        }))
    .pipe(gulp.dest("."))
);

gulp.task('babel-for-ie11', () => // TODO: can probably just copy if we're happy that safari doesn't die!
    gulp.src([
        'node_modules/react-async/dist-web/index.js'
    ], {
        cwd: "../../../../../../../../" // For yarn.  If pnpm then it'll be follow: true
    })
    .pipe(babel({ // TODO: Remove these three lines to just make it copy with rename
        presets: ['@babel/preset-env'],
        plugins: ["@babel/plugin-transform-modules-umd"]


    }))
    .pipe(gulp.dest("scripts/lib/react-async"))
);

gulp.task('copy-js', () => // TODO: Also copy src/.../build to target/
    merge(
        gulp.src([
            'node_modules/@eccosolutions/rjsf-core/dist/react-jsonschema-form.js',
            'node_modules/json-patch-gen/lib/diff.js',
            'node_modules/qr-scanner/qr-scanner.legacy.min.js',
            'node_modules/react-router/umd/react-router.js',
            'node_modules/react-router/umd/react-router.min.js',
            'node_modules/react-router-dom/umd/react-router-dom.js',
            'node_modules/react-router-dom/umd/react-router-dom.min.js',
            'node_modules/@cubejs-client/core/dist/cubejs-client-core.umd.js',
            'node_modules/@cubejs-client/react/dist/cubejs-client-react.umd.js',
            'node_modules/@cubejs-client/ws-transport/dist/cubejs-client-ws-transport.umd.js',
            'node_modules/chart.js/dist/chart.umd.js',

            'ecco-offline/src/main/resources/com/ecco/offline/staticFiles/node_modules/rxjs/bundles/Rx.js',
            'ecco-offline/src/main/resources/com/ecco/offline/staticFiles/node_modules/rxjs/bundles/Rx.min.js'
            ], {
                // gulp uses cwd to combine it with the relative paths in src to make absolute locations - so we get the project root
                cwd: "../../../../../../../../" // For yarn.  If pnpm then it'll be follow: true
            })
            .pipe(gulp.dest('scripts/lib/')),
        gulp.src([
            "node_modules/ecco-admin/*/ecco-admin*.js",
            "node_modules/ecco-calendar/*/ecco-calendar*.js",
            "node_modules/@eccosolutions/ecco-common/*/ecco-common*.js",
            "node_modules/ecco-commands/*/ecco-commands*.js",
            "node_modules/ecco-components/*/ecco-components*.js",
            "node_modules/ecco-cubejs/*/ecco-cubejs*.js",
            "node_modules/@eccosolutions/ecco-crypto/*/ecco-crypto*.js",
            "node_modules/ecco-dto/*/ecco-dto*.js",
            "node_modules/ecco-evidence/*/ecco-evidence*.js",
            "node_modules/ecco-finance/*/ecco-finance*.js",
            "node_modules/ecco-incidents/*/ecco-incidents*.js",
            "node_modules/ecco-managedvoids/*/ecco-managedvoids*.js",
            "node_modules/ecco-repairs/*/ecco-repairs*.js",
            "node_modules/ecco-forms/*/ecco-forms*.js",
            "node_modules/ecco-math/*/ecco-math*.js",
            "node_modules/@eccosolutions/ecco-mui/*/ecco-mui*.js",
            "node_modules/@eccosolutions/ecco-mui-controls/*/ecco-mui-controls*.js",
            "node_modules/ecco-offline-data/*/ecco-offline-data*.js",
            "node_modules/ecco-rota/*/ecco-rota*.js"
        ], {
                cwd: "../../../../../../../../"
            })
            .pipe(gulp.dest("../../../../../../../target/classes/com/ecco/offline/staticFiles/build")),
        gulp.src([
                "node_modules/ecco-staff-app/build/**"
            ], {
                cwd: "../../../../../../../../"
            })
            .pipe(gulp.dest("../../../../../../../target/classes/com/ecco/offline/staticFiles/build/dist/ecco-app")),
        gulp.src([
                "node_modules/ecco-test-app/build/**"
            ], {
                cwd: "../../../../../../../../"
            })
            .pipe(gulp.dest("../../../../../../../target/classes/com/ecco/offline/staticFiles/build/dist/ecco-test"))
    )
);

gulp.task('ensure-package', (cb) => {
    // Write a file on src/.../scripts to ensure IDEA sees a src change to trigger ecco-offline WAR to be repackaged
    fs.writeFile(".trigger-package", "Written by Gulpfile.js", cb);
});

gulp.task('pre-tomcat',
  gulp.series(
    "copy-js",
    "ensure-package")
);

// TODO: Use a different tsconfig for testing if we can get away with it - as ts-jest doesn't yet support composite=true
gulp.task('jest', () =>
    gulp.src('__tests__').pipe(jest({
        globals: {
            "ts-jest": {
                compiler: {
                    // an attempt to make things behave >@ 23.10.4
                    esModuleInterop: true
                }
            }
        },
        "preprocessorIgnorePatterns": [
            "build/", "node_modules/"
        ],
        "automock": false
    }))
);

gulp.task("default", gulp.series(
    "clean",
    gulp.parallel("tsc", "babel-for-ie11", "copy-js", "minify-js"),
    "ensure-package"
));

gulp.task("copy-files", gulp.series(
  gulp.parallel("babel-for-ie11", "copy-js", "minify-js"),
  "ensure-package"
));

gulp.task("emit", gulp.series(
    "clean",
    gulp.parallel("tsc", "babel-for-ie11", "copy-js", "minify-js"),
    "ensure-package"
));
