
/*
 * Before referencing this script, set the following global variables:
 *
 *      requirejs_baseUrl (string): The base path for loading modules from
 *
 * This script should be loaded only once.  Afterwards, you can use
 * <script>require(['a', 'b']);</script> to include component modules.
 * Note: even for one entry, you must pass an array i.e. require(['modulename']);
 */

// Detect online page and want to redirect to /ui/scripts/ instead of cachebust
// For now this is disabled as we make things worse by having request -> 304 Not Modified on these resources when
// online.  We need to create PURE App pages
var redirectToOffline = false; // WILL BE ->  !(requirejs_baseUrl.indexOf("ui/scripts/") > -1 || requirejs_baseUrl == "scripts/");

// relative to e.g. /uat/resources/174b6877281/build/
var pathToAppCacheScripts = redirectToOffline ? "../../../ui/scripts/" : "../scripts/";

// chooses non-minified, development versions for React etc
function isDev() {
    // 'devMode' is provided from the environment as a string, so we want to make
    // sure that its not truthy
    return (typeof requirejs_devMode != 'undefined') && requirejs_devMode.toLowerCase() === "true";
}

// For DX: ensure we have an absolute URL so require doesn't get upset with lazy loading
const baseUrl = isDev()
    ? new URL(requirejs_baseUrl + "../build/", window.location.href).href
    : requirejs_baseUrl + "../build/";

// relative to e.g. /uat/resources/174b6877281/build/
const pathToEccoLibs = (isDev() && requirejs_baseUrl.startsWith("/ecco-war") ? "" : "../../../ui/build/")
    + (isDev() ? "debug/" : "dist/");

requirejs.config({
        waitSeconds: 60, // Default is 7
        baseUrl: baseUrl,
        paths: {
            "ecco-admin": pathToEccoLibs + "ecco-admin",
            "ecco-calendar": pathToEccoLibs + "ecco-calendar",
            "@eccosolutions/ecco-common": pathToEccoLibs + "ecco-common",
            "ecco-commands": pathToEccoLibs + "ecco-commands",
            "ecco-components": pathToEccoLibs + "ecco-components",
            "ecco-cubejs": pathToEccoLibs + "ecco-cubejs",
            "@eccosolutions/ecco-crypto": pathToEccoLibs + "ecco-crypto",
            "ecco-dto": pathToEccoLibs + "ecco-dto",
            "ecco-evidence": pathToEccoLibs + "ecco-evidence",
            "ecco-finance": pathToEccoLibs + "ecco-finance",
            // "ecco-forms": pathToEccoLibs + "ecco-forms",
            "ecco-forms": "dist/" + "ecco-forms", // NOTE: Avoid 26Mb debug build until we've sorted out react-jsonschema-forms-extension bundling
            "ecco-incidents": pathToEccoLibs + "ecco-incidents",
            "ecco-managedvoids": pathToEccoLibs + "ecco-managedvoids",
            "ecco-repairs": pathToEccoLibs + "ecco-repairs",
            "ecco-math": pathToEccoLibs + "ecco-math",
            "@eccosolutions/ecco-mui": pathToEccoLibs + "ecco-mui",
            "@eccosolutions/ecco-mui-controls": pathToEccoLibs + "ecco-mui-controls",
            "ecco-offline-data": pathToEccoLibs + "ecco-offline-data",
            "ecco-rota": pathToEccoLibs + "ecco-rota",

            "application-properties": "../scripts/application-properties",
            "bootstrap": pathToAppCacheScripts + "lib/bootstrap.min",
            "bootstrap-modal": pathToAppCacheScripts + "lib/bootstrap-modal.min",
            "bootstrap-modalmanager": pathToAppCacheScripts + "lib/bootstrap-modalmanager.min",

            // datatables.net is a required module name
            "datatables.net": pathToAppCacheScripts + "datatables/media/js/jquery.dataTables.min",
            // datatables.net-bs is a required module name
            "datatables.net-bs": pathToAppCacheScripts + "datatables/media/js/dataTables.bootstrap.min",
            "jszip": pathToAppCacheScripts + "jszip/dist/jszip.min",
            "datatables.net-buttons": pathToAppCacheScripts + "datatables/extensions/Buttons/js/dataTables.buttons.min",
            "datatables.net-buttons-html5": pathToAppCacheScripts + "datatables/extensions/Buttons/js/buttons.html5",
            "datatables.net-buttons-bs": pathToAppCacheScripts + "datatables/extensions/Buttons/js/buttons.bootstrap.min",
            "datatables.net-moment": pathToAppCacheScripts + "datatables/plug-ins/1.10.19/sorting/datetime-moment",

            "json-patch-gen": pathToAppCacheScripts + "lib/diff",
            "document-register-element": pathToAppCacheScripts + "polyfill/document-register-element",

            "bowser": pathToAppCacheScripts + "lib/bowser.min",
            "eccocalendar": "../build/calendar",
            "hopscotch": pathToAppCacheScripts + "lib/hopscotch.min",
            "@eccosolutions/rjsf-core": pathToAppCacheScripts + "lib/react-jsonschema-form",

            // CubeJS client libraries
            "cubejs-client-core": pathToAppCacheScripts + "lib/cubejs-client-core.umd",
            "cubejs-client-react": pathToAppCacheScripts + "lib/cubejs-client-react.umd",
            "cubejs-client-ws-transport": pathToAppCacheScripts + "lib/cubejs-client-ws-transport.umd",

            // Chart.js libraries
            "chart.js": pathToAppCacheScripts + "lib/chart.umd",
            "react-chartjs-2": pathToAppCacheScripts + "lib/index",

            // NOTE: https://github.com/jrburke/requirejs/issues/435 (must be as jquery for jquery to behave)
            "jquery-lib": pathToAppCacheScripts + "jquery/jquery-3.6.0.min",
            "jquery": pathToAppCacheScripts + "jquery/jquery-migrate.min",
            "jquery-bundle": pathToAppCacheScripts + "jquery/jquery-bundle",
            "jquery-jqplot-bundle": "../scripts/jquery/jquery-jqplot-bundle",
            "jquery-file-upload": "../scripts/jquery-file-upload-9.5.8/jquery.fileupload.min",
            "jquery-iframe-transport": "../scripts/jquery-file-upload-9.5.8/jquery.iframe-transport",
            "jquery-ui": pathToAppCacheScripts + "jquery/jquery-ui-1.10.3.custom.min",
            "jquery-ui-bundle": pathToAppCacheScripts + "jquery/jquery-ui-bundle",
            "jquery-ui-touch-punch": pathToAppCacheScripts + "jquery/jquery-ui-touch-punch",
            "jquery-ui-datepicker": pathToAppCacheScripts + "jquery/jquery-datepicker",
            "jquery-ui-timepicker": pathToAppCacheScripts + "jquery/jquery-timepicker.min",
            "jquery-popbox" : "../scripts/jquery/jquery-popbox",
            "jquery-popboxInit" : "../scripts/jquery/jquery-popboxInit",
            "lodash": pathToAppCacheScripts + "lib/lodash.min",

            "jquery-amd/common": "../scripts/jquery-amd/common",
            "jquery-amd/dateBox": "../scripts/jquery-amd/dateBox",
            "jquery-amd/filter-data-comment-type": "../scripts/jquery-amd/filter-data-comment-type",
            "jquery-amd/jquery.fileupload": "../scripts/jquery-amd/jquery.fileupload",
            "jquery-amd/jquery.fileupload-ui": "../scripts/jquery-amd/jquery.fileupload-ui",

            "jquery-browser": "../scripts/jquery.browser.min",
            "jquery-jqplot": "../scripts/jquery.jqplot.min",
            "jqplot-donutRenderer": "../scripts/jqplot-plugins/jqplot.donutRenderer.min",
            "jqplot-pieRenderer": "../scripts/jqplot-plugins/jqplot.pieRenderer.min",
            "jqplot-barRenderer": "../scripts/jqplot-plugins/jqplot.barRenderer.min",
            "jqplot-pointLabels": "../scripts/jqplot-plugins/jqplot.pointLabels.min",
            "jqplot-categoryAxisRenderer": "../scripts/jqplot-plugins/jqplot.categoryAxisRenderer.min",
            "jqplot-canvasTextRenderer": "../scripts/jqplot-plugins/jqplot.canvasTextRenderer.min",
            "jqplot-canvasAxisTickRenderer": "../scripts/jqplot-plugins/jqplot.canvasAxisTickRenderer.min",
            "lazy": "../scripts/lib/lazy.min",
            "moment": pathToAppCacheScripts + "time/moment.min",

            // - See map below which maps styles to core: // "@material-ui/styles": pathToEccoLibs + "ecco-mui",

            "qr-scanner": pathToAppCacheScripts + "lib/qr-scanner.legacy.min",
            "qunit": "../scripts/tests/qunit/qunit-1.11.0",
            "raphael": pathToAppCacheScripts + "draw/raphael",
            "react": pathToAppCacheScripts + "lib/react." + (isDev() ? "development" : "production.min"),
            "react-async": pathToAppCacheScripts + "lib/react-async/index",
            "react-dom": pathToAppCacheScripts + "lib/react-dom." + (isDev() ? "development" : "production.min"),
            "react-addons-create-fragment": pathToAppCacheScripts + "lib/react-addons-create-fragment.min",
            "react-addons-transition-group": pathToAppCacheScripts + "lib/react-transition-group.min",
            "react-bootstrap": pathToAppCacheScripts + "lib/react-bootstrap" + (isDev() ? "" : ".min"),
            "react-router-dom": pathToAppCacheScripts + "lib/react-router-dom" + (isDev() ? "" : ".min"), // UMD build contains react-router
            "prop-types": pathToAppCacheScripts + "lib/prop-types",
            "trim-canvas": pathToAppCacheScripts + "lib/trim-canvas",
            "rxjs/observable/concat": pathToAppCacheScripts + "lib/rxjs-observable-concat",
            "rxjs/operators": pathToAppCacheScripts + "lib/rxjs-operators",
            "rxjs": pathToAppCacheScripts + "lib/Rx" + (isDev() ? "" : ".min"),
            "select2": pathToAppCacheScripts + "lib/select2/select2.min", // shim only
            "bloodhound": pathToAppCacheScripts + "lib/bloodhound.min",
            "tslib": pathToAppCacheScripts + "lib/tslib",
            "typeahead": "../scripts/jquery-amd/typeahead.jquery.min",
            "URI": pathToAppCacheScripts + "lib/URI"
        },
        map: {
            '*': {
                'react-router' : 'react-router-dom', // because react-router-dom contains react-router
                "@material-ui/core": "@eccosolutions/ecco-mui",
                "@material-ui/lab": "@eccosolutions/ecco-mui",
                "@material-ui/pickers": "@eccosolutions/ecco-mui",
                // "@material-ui/icons": pathToEccoLibs + "ecco-components", // use ecco-components for icons
                '@material-ui/styles': '@eccosolutions/ecco-mui', // because styles are in /core
                'moment': '../scripts/time/moment-init',
                // CubeJS client library mappings
                '@cubejs-client/core': 'cubejs-client-core',
                '@cubejs-client/react': 'cubejs-client-react',
                '@cubejs-client/ws-transport': 'cubejs-client-ws-transport'
            },
            '../scripts/time/moment-init': { 'moment': 'moment' }
        },
        shim: {
            "json-patch-gen": {
                // exports: "window.diff" doesn't work, so use init to return the exported impl instead
                init: function() {
                    return window.diff;
                }
                },
            "qunit": {
                exports: "QUnit",
                init: function() {
                    QUnit.config.autoload = false;
                    QUnit.config.autostart = false;
                }
            },
            "bootstrap": ["jquery","bootstrap-modal"],
            "bootstrap-modal": ["jquery", "bootstrap-modalmanager"],
            "bootstrap-modalmanager": ["jquery"],
            "jquery": ["jquery-lib"], // We have to load jquery-lib before we load migrate
            "jquery-ui": ["jquery"],
            "jquery-ui-touch-punch": ["jquery-ui"],
            "jquery-ui-datepicker" : ["jquery-ui"],
            "jquery-ui-timepicker" : ["jquery-ui-touch-punch", "jquery-ui-datepicker"],
            "jquery-popbox": ["jquery"],
            "jquery-popboxInit": ["jquery-popbox"],
            "select2": ["jquery"],

            // CubeJS client library dependencies
            "cubejs-client-react": ["react", "cubejs-client-core"],
            "cubejs-client-ws-transport": ["cubejs-client-core"],

            // Chart.js library dependencies
            "react-chartjs-2": ["react", "chart.js"],

            // shims required for our modules
            "attachments/attachments": ["bootstrap", "jquery-file-upload", "jquery-iframe-transport"], // for .dropdown and $.fileUpload
            "controls/Accordion": ["bootstrap"], // for .panel-collapse
            "controls/AutoCompleteInput": ["typeahead"],
            "controls/Modal": ["bootstrap-modal", "bootstrap-modalmanager"], // for .modal
            "controls/TabbedContainer": ["bootstrap"], // for .nav-tabs
            'data-attr/ListDefSelect2List': ['select2'],
            'feature-config/EditListDefForm': ['select2'],

            // for reports
            "datatables.net": ['jquery'],
            "datatables.net-bs": ['datatables.net'],
            "datatables.net-buttons": ['datatables.net', 'jszip'],
            "datatables.net-buttons-html5": ['datatables.net-buttons'],
            "datatables.net-buttons-bs": ['datatables.net-buttons-html5', 'datatables.net-bs'],
            "datatables.net-moment": ['datatables.net', 'moment'],
            'jquery-browser': ['jquery'],
            'jquery-jqplot': ['jquery','jquery-browser'],
            'jqplot-donutRenderer': ['jquery-jqplot'],
            'jqplot-pieRenderer': ['jquery-jqplot'],
            'jqplot-barRenderer': ['jquery-jqplot'],
            'jqplot-categoryAxisRenderer': ['jquery-jqplot'],
            'jqplot-canvasTextRenderer': ['jquery-jqplot'],
            'jqplot-canvasAxisTickRenderer': ['jquery-jqplot'],
            'jqplot-pointLabels': ['jquery-jqplot'],
            'controls/tables': ['datatables.net-buttons-html5', 'datatables.net-buttons-bs', 'datatables.net-moment'],
            'reports/charts/EditCriteriaForm': ['select2']
         }
    });

// These are optional dependencies for URI.js.
// We don't use them, so stub them out.
define("punycode", function () { return null; });
define("IPv6", function () { return null; });
define("SecondLevelDomains", function () { return null; });

